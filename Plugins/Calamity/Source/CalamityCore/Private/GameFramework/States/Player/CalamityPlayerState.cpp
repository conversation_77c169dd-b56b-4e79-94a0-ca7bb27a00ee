#include "GameFramework/States/Player/CalamityPlayerState.h"

#include "AbilitySystem/Components/CalamityAbilitySystemComponent.h"
#include "Net/UnrealNetwork.h"

ACalamityPlayerState::ACalamityPlayerState(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	// Create and set up inventory manager
	InventoryManager = CreateDefaultSubobject<UNinjaInventoryManagerComponent>(TEXT("InventoryManager"));
	InventoryManager->SetIsReplicated(true);
    
	// Create the custom Ability System Component using the object initializer
	/*AbilitySystemComponent = ObjectInitializer.CreateDefaultSubobject<UCalamityAbilitySystemComponent>(this, TEXT("AbilitySystem"));
	if (IsValid(AbilitySystemComponent))
	{
		AbilitySystemComponent->SetIsReplicated(true);
		AbilitySystemComponent->SetReplicationMode(AbilityReplicationMode);
	}*/

	// We need to defer attribute set creation until after BeginPlay
	// so we can properly add it to the ASC from the parent class
}

void ACalamityPlayerState::BeginPlay()
{
	Super::BeginPlay();
    
	// Create the attribute set and add it to the ASC after the parent has initialized
	if (GetAbilitySystemComponent())
	{
		AttributeSet = NewObject<UNinjaCombatAttributeSet>(this, TEXT("AttributeSet"));
		if (AttributeSet)
		{
			GetAbilitySystemComponent()->AddAttributeSetSubobject(AttributeSet);
		}
	}
}

void ACalamityPlayerState::RecordKill()
{
	// Ensure this logic only runs on the server
	if (HasAuthority())
	{
		MatchStats.Kills++;
		// Force net update if needed immediately, often replication handles it fine
		// ForceNetUpdate();

		// If not using OnRep_, you might need an RPC to tell the owning client specifically
		// Client_UpdateHUDStats(MatchStats);
	}
}

void ACalamityPlayerState::RecordDeath()
{
	if (HasAuthority())
	{
		MatchStats.Deaths++;
	}
}

void ACalamityPlayerState::RecordAssist()
{
	// Ensure this logic only runs on the server
	if (HasAuthority())
	{
		MatchStats.Assists++;
		// Force net update if needed immediately, often replication handles it fine
		// ForceNetUpdate();

		// If not using OnRep_, you might need an RPC to tell the owning client specifically
		// Client_UpdateHUDStats(MatchStats);
	}
}

void ACalamityPlayerState::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	DOREPLIFETIME(ACalamityPlayerState, MatchStats);
}

void ACalamityPlayerState::OnRep_MatchStats()
{
	// Called on clients when MatchStats changes due to replication.
	// Use this to trigger UI updates (e.g., update the HUD).
	// You might broadcast a delegate/event here that the HUD widget listens to.
	 OnStatsChangedDelegate.Broadcast(MatchStats);
}
