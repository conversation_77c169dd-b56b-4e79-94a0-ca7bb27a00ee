#pragma once

#include "CoreMinimal.h"
#include "GameFramework/NinjaGASPlayerState.h"
#include "Components/NinjaInventoryManagerComponent.h"
#include "Interfaces/InventoryManagerProviderInterface.h"
#include "AbilitySystem/NinjaCombatAttributeSet.h"
#include "GameFramework/Systems/CalamityGameStats.h"
#include "CalamityPlayerState.generated.h"

// Declare the delegate type
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnStatsChangedDelegate, const FPlayerMatchStats&, NewStats);


/**
 * Player state for Calamity game, inherits from NinjaGASPlayerState
 */
UCLASS()
class CALAMITYCORE_API ACalamityPlayerState : public ANinjaGASPlayerState, public IInventoryManagerProviderInterface
{
	GENERATED_BODY()

public:
	ACalamityPlayerState(const FObjectInitializer& ObjectInitializer);
	// Override BeginPlay to set up attributes after parent initialization
	virtual void BeginPlay() override;
                           
    // IInventoryManagerProviderInterface
    virtual UNinjaInventoryManagerComponent* GetInventoryManager_Implementation() const override { return InventoryManager; }

	// Getter for the attribute set
	UFUNCTION(BlueprintCallable, Category = "Calamity|Attributes")
	UNinjaCombatAttributeSet* GetAttributeSet() const { return AttributeSet; }

	// Called from GameMode when this player gets a kill
	void RecordKill();
	// Called from GameMode when this player dies
	void RecordDeath();
	// Called from GameMode when this player gets an assist, etc.
	void RecordAssist();
	// ... other stat recording functions
	
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	
	UFUNCTION()
	void OnRep_MatchStats();

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnStatsChangedDelegate OnStatsChangedDelegate;

protected:
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Calamity|Inventory")
	UNinjaInventoryManagerComponent* InventoryManager;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Calamity|Attributes")
	UNinjaCombatAttributeSet* AttributeSet;

	// Make sure it replicates so clients can see scores, HUD updates, etc.
	UPROPERTY(ReplicatedUsing = OnRep_MatchStats, VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
	FPlayerMatchStats MatchStats;
	
};